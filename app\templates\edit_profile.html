<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人信息管理 - 教材管理系统</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 按钮渐变样式 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        
        .btn-purple {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            transition: all 0.3s ease;
        }
        .btn-purple:hover {
            background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(139, 92, 246, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }
        
        /* 按钮禁用状态样式 */
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }
        
        /* 确保禁用状态的按钮悬停时不改变样式 */
        button:disabled:hover {
            transform: none !important;
            box-shadow: none !important;
        }



        /* 编辑状态过渡动画 */
        .edit-transition {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .edit-transition input {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }



        /* 表单字段过渡效果 */
        .form-field-transition {
            transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
        }

        /* Alpine.js cloak */
        [x-cloak] {
            display: none !important;
        }

        /* CustomSelect样式 */
        .custom-select {
            position: relative;
        }

        .custom-select-trigger {
            width: 100%;
            height: 48px;
            padding: 12px 40px 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            background-color: white;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .custom-select-trigger:hover {
            border-color: #9ca3af;
        }

        .custom-select.active .custom-select-trigger {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-select-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.2s ease;
            color: #6b7280;
            font-size: 12px;
        }

        .custom-select.active .custom-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }

        .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            z-index: 9999;
            max-height: 240px;
            overflow: hidden;
            margin-top: 4px;
            opacity: 0;
            transform: translateY(-10px);
            visibility: hidden;
            transition: all 0.2s ease;
        }

        .custom-select.active .custom-select-dropdown {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        .custom-select-search {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }

        .custom-select-search .search-input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 13px;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .custom-select-search .search-input:focus {
            border-color: #3b82f6;
        }

        .custom-select-options {
            max-height: 180px;
            overflow-y: auto;
        }

        .custom-select-option {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: background-color 0.15s ease;
        }

        .custom-select-option:hover {
            background-color: #f3f4f6;
        }

        .custom-select-option.selected {
            background-color: #3b82f6;
            color: white;
        }

        .custom-select-option.no-results {
            color: #9ca3af;
            cursor: default;
            text-align: center;
            padding: 16px 12px;
        }

        .custom-select-options::-webkit-scrollbar {
            width: 4px;
        }

        .custom-select-options::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 2px;
        }

        .custom-select-options::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 2px;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <!-- 消息通知组件 -->
    <div id="messageContainer" class="fixed top-6 right-6 z-50 space-y-3">
        <!-- 消息将通过JavaScript动态添加 -->
    </div>
    
    <div class="container mx-auto px-6 py-8">
        <div class="max-w-6xl mx-auto space-y-6">
            <!-- 基本信息卡片 -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 overflow-hidden" x-data="profileManager()" x-init="init()" x-cloak>
                <div class="p-6 border-b border-slate-100">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-xl font-bold text-slate-800 flex items-center">
                                <i class="fas fa-user-circle text-blue-500 mr-3"></i>
                                基本信息
                            </h2>
                            <p class="text-sm text-slate-600 mt-1">查看您的个人基本信息</p>
                        </div>

                    </div>
                </div>
                <div class="p-6">
                    <!-- 加载状态 -->
                    <div x-show="loading" class="flex items-center justify-center py-8">
                        <div class="flex items-center space-x-3">
                            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                            <span class="text-slate-600">正在加载用户信息...</span>
                        </div>
                    </div>

                    <!-- 基本信息展示 -->
                    <div class="space-y-6" x-show="!loading">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- 用户名 -->
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">用户名</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-user text-slate-400"></i>
                                    </div>
                                    <input type="text"
                                           class="w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none text-slate-700"
                                           x-bind:value="formData.username || ''" readonly>
                                </div>
                            </div>

                            <!-- 角色 -->
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">用户角色</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-user-tag text-slate-400"></i>
                                    </div>
                                    <input type="text"
                                           class="w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none text-slate-700"
                                           x-bind:value="getRoleDisplayName(userRole)" readonly>
                                </div>
                            </div>

                            <!-- 所属单位 -->
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">
                                    <span x-text="userRole === 'teacher' ? '所属学校' : userRole === 'publisher' ? '出版社' : userRole === 'dealer' ? '经销商' : '所属单位'"></span>
                                </label>
                                <!-- 教师用户编辑时显示学校选择器 -->
                                <div x-show="userRole === 'teacher' && isEditing" class="custom-select" id="schoolSelectContainer">
                                    <!-- CustomSelect会动态生成内容 -->
                                </div>
                                <!-- 非教师用户或非编辑状态显示只读输入框 -->
                                <div x-show="userRole !== 'teacher' || !isEditing" class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-building text-slate-400"></i>
                                    </div>
                                    <input type="text"
                                           class="w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none text-slate-700"
                                           x-bind:value="organizationName || '未设置'" readonly>
                                </div>
                            </div>

                            <!-- 姓名 -->
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">姓名</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-id-card text-slate-400"></i>
                                    </div>
                                    <input type="text"
                                           x-bind:class="isEditing ? 'w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-slate-700' : 'w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none text-slate-700'"
                                           x-bind:readonly="!isEditing"
                                           x-model="formData.name"
                                           placeholder="请输入姓名">
                                </div>
                            </div>
                        </div>

                        <!-- 手机号码 -->
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">手机号码</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-mobile-alt text-slate-400"></i>
                                </div>
                                <input type="tel"
                                       x-bind:class="isEditing ? 'w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-slate-700' : 'w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none text-slate-700'"
                                       x-bind:readonly="!isEditing"
                                       x-model="formData.phone_number"
                                       placeholder="请输入手机号码">
                            </div>
                        </div>

                        <!-- 邮箱 -->
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">邮箱</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-envelope text-slate-400"></i>
                                </div>
                                <input type="email"
                                       x-bind:class="isEditing ? 'w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-slate-700' : 'w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none text-slate-700'"
                                       x-bind:readonly="!isEditing"
                                       x-model="formData.email"
                                       placeholder="请输入邮箱地址">
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="pt-4 flex justify-end space-x-4">
                            <!-- 编辑按钮 (默认显示) -->
                            <div x-show="!isEditing" class="flex space-x-4">
                                <button type="button" x-on:click="startEditing()"
                                        class="bg-blue-500 hover:bg-blue-600 text-white font-medium h-10 px-4 rounded-lg flex items-center space-x-2 text-sm transition-colors">
                                    <i class="fas fa-edit"></i>
                                    <span>编辑个人信息</span>
                                </button>
                                <button type="button" x-on:click="openUsernameModal()"
                                        class="bg-green-500 hover:bg-green-600 text-white font-medium h-10 px-4 rounded-lg flex items-center space-x-2 text-sm transition-colors">
                                    <i class="fas fa-user-edit"></i>
                                    <span>修改用户名</span>
                                </button>
                                <button type="button" x-on:click="openPasswordModal()"
                                        class="btn-purple text-white font-medium h-10 px-4 rounded-lg flex items-center space-x-2 text-sm">
                                    <i class="fas fa-key"></i>
                                    <span>修改密码</span>
                                </button>
                            </div>

                            <!-- 保存/取消按钮 (编辑时显示) -->
                            <div x-show="isEditing" class="flex space-x-4">
                                <button type="button" x-on:click="cancelEditing()"
                                        class="px-6 py-3 bg-slate-500 text-white rounded-xl hover:bg-slate-600 transition-colors font-medium">
                                    取消
                                </button>
                                <button type="button" x-on:click="saveProfileChanges()"
                                        class="px-6 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors font-medium">
                                    保存
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 角色信息卡片 (仅教师用户显示) -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 overflow-hidden" x-data="teacherInfoManager()" x-init="init()" x-show="showCard">
                <div class="p-6 border-b border-slate-100">
                    <h2 class="text-xl font-bold text-slate-800 flex items-center">
                        <i class="fas fa-graduation-cap text-green-500 mr-3"></i>
                        角色信息
                    </h2>
                    <p class="text-sm text-slate-600 mt-1">管理您的教师角色相关信息</p>
                </div>
                <div class="p-6">
                    <!-- 加载状态 -->
                    <div x-show="loading" class="flex items-center justify-center py-8">
                        <div class="flex items-center space-x-3">
                            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-500"></div>
                            <span class="text-slate-600">正在加载角色信息...</span>
                        </div>
                    </div>

                    <form class="space-y-6" x-show="!loading">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- 院系 -->
                            <div>
                                <label for="department" class="block text-sm font-medium text-slate-700 mb-2">院系</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-university text-slate-400"></i>
                                    </div>
                                    <input type="text" id="department" name="department"
                                           x-bind:class="isEditing ? 'w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent form-field-transition text-slate-700' : 'w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none form-field-transition text-slate-700'"
                                           x-bind:readonly="!isEditing"
                                           x-model="formData.department"
                                           placeholder="请输入院系">
                                </div>
                            </div>

                            <!-- 职务 -->
                            <div>
                                <label for="position" class="block text-sm font-medium text-slate-700 mb-2">职务</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-briefcase text-slate-400"></i>
                                    </div>
                                    <input type="text" id="position" name="position"
                                           x-bind:class="isEditing ? 'w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent form-field-transition text-slate-700' : 'w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none form-field-transition text-slate-700'"
                                           x-bind:readonly="!isEditing"
                                           x-model="formData.position"
                                           placeholder="请输入职务">
                                </div>
                            </div>

                            <!-- 职称 -->
                            <div>
                                <label for="title" class="block text-sm font-medium text-slate-700 mb-2">职称</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-award text-slate-400"></i>
                                    </div>
                                    <input type="text" id="title" name="title"
                                           x-bind:class="isEditing ? 'w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent form-field-transition text-slate-700' : 'w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none form-field-transition text-slate-700'"
                                           x-bind:readonly="!isEditing"
                                           x-model="formData.title"
                                           placeholder="请输入职称">
                                </div>
                            </div>

                            <!-- 性别 -->
                            <div>
                                <label for="gender" class="block text-sm font-medium text-slate-700 mb-2">性别</label>
                                <!-- 展示状态 -->
                                <div x-show="!isEditing" class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-venus-mars text-slate-400"></i>
                                    </div>
                                    <input type="text"
                                           class="w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none form-field-transition text-slate-700"
                                           x-bind:value="formData.gender || '未设置'" readonly>
                                </div>
                                <!-- 编辑状态 -->
                                <div x-show="isEditing"
                                     x-transition:enter="transition ease-out duration-300"
                                     x-transition:enter-start="opacity-0 transform scale-95"
                                     x-transition:enter-end="opacity-100 transform scale-100"
                                     x-transition:leave="transition ease-in duration-200"
                                     x-transition:leave-start="opacity-100 transform scale-100"
                                     x-transition:leave-end="opacity-0 transform scale-95">
                                    <div class="custom-select" id="genderSelectContainer">
                                        <div class="custom-select-trigger">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-venus-mars text-slate-400"></i>
                                            </div>
                                            <span class="custom-select-text pl-7">请选择性别</span>
                                            <i class="fas fa-chevron-down custom-select-arrow"></i>
                                        </div>
                                        <div class="custom-select-dropdown">
                                            <div class="custom-select-options" id="genderSelectOptions">
                                                <!-- 选项将动态生成 -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="pt-4 flex justify-end space-x-4 min-h-[48px]">
                            <!-- 编辑按钮 (默认显示) -->
                            <div x-show="!isEditing" class="flex space-x-4">
                                <button type="button" x-on:click="startEditing()"
                                        class="bg-green-500 hover:bg-green-600 text-white font-medium h-12 px-6 rounded-xl flex items-center space-x-2 shadow-lg transition-colors duration-200">
                                    <i class="fas fa-edit"></i>
                                    <span>编辑角色信息</span>
                                </button>
                            </div>

                            <!-- 保存和取消按钮 (编辑时显示) -->
                            <div x-show="isEditing" class="flex space-x-4">
                                <button type="button" x-on:click="cancelEditing()"
                                        class="px-6 py-3 bg-slate-500 text-white rounded-xl hover:bg-slate-600 font-medium h-12 flex items-center space-x-2 transition-colors duration-200">
                                    <i class="fas fa-times"></i>
                                    <span>取消</span>
                                </button>

                                <button type="button" x-on:click="saveTeacherInfo()"
                                        class="bg-green-500 hover:bg-green-600 text-white font-medium h-12 px-6 rounded-xl flex items-center space-x-2 shadow-lg transition-colors duration-200">
                                    <i class="fas fa-save"></i>
                                    <span>保存角色信息</span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 主讲课程管理卡片 (仅教师用户显示) -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 overflow-hidden" x-data="courseManager()" x-init="init()" x-show="showCard">
                <div class="p-6 border-b border-slate-100">
                    <h2 class="text-xl font-bold text-slate-800 flex items-center">
                        <i class="fas fa-chalkboard-teacher text-green-500 mr-3"></i>
                        主讲课程管理
                    </h2>
                    <p class="text-sm text-slate-600 mt-1">管理您的主讲课程信息</p>
                </div>
                <div class="p-6">
                    <!-- 加载状态 -->
                    <div x-show="loading" class="flex items-center justify-center py-8">
                        <div class="flex items-center space-x-3">
                            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-500"></div>
                            <span class="text-slate-600">正在加载课程信息...</span>
                        </div>
                    </div>

                    <!-- 课程表格 -->
                    <div x-show="!loading" class="space-y-4">
                        <!-- 表格头部 -->
                        <div class="overflow-x-auto">
                            <table class="w-full border-collapse">
                                <thead>
                                    <tr class="bg-slate-50">
                                        <th class="border border-slate-200 px-4 py-3 text-left text-sm font-medium text-slate-700">课程名称</th>
                                        <th class="border border-slate-200 px-4 py-3 text-left text-sm font-medium text-slate-700">学期</th>
                                        <th class="border border-slate-200 px-4 py-3 text-left text-sm font-medium text-slate-700">课程类型</th>
                                        <th class="border border-slate-200 px-4 py-3 text-left text-sm font-medium text-slate-700">学生人数</th>
                                        <th class="border border-slate-200 px-4 py-3 text-center text-sm font-medium text-slate-700">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <template x-for="(course, index) in courses" :key="course.id || 'new-' + index">
                                        <tr>
                                            <td class="border border-slate-200 px-4 py-3">
                                                <input type="text"
                                                       x-model="course.course_name"
                                                       x-bind:readonly="!course.editing"
                                                       x-bind:class="course.editing ? 'w-full px-2 py-1 border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500' : 'w-full bg-transparent border-none outline-none'"
                                                       placeholder="请输入课程名称">
                                            </td>
                                            <td class="border border-slate-200 px-4 py-3">
                                                <input type="text"
                                                       x-model="course.semester"
                                                       x-bind:readonly="!course.editing"
                                                       x-bind:class="course.editing ? 'w-full px-2 py-1 border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500' : 'w-full bg-transparent border-none outline-none'"
                                                       placeholder="如：2024春季">
                                            </td>
                                            <td class="border border-slate-200 px-4 py-3">
                                                <template x-if="!course.editing">
                                                    <div class="w-full bg-transparent" x-text="course.course_type || '未设置'"></div>
                                                </template>
                                                <template x-if="course.editing">
                                                    <select x-model="course.course_type"
                                                            class="w-full px-2 py-1 border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500">
                                                        <option value="">请选择课程性质</option>
                                                        <option value="专业课">专业课</option>
                                                        <option value="公共课">公共课</option>
                                                    </select>
                                                </template>
                                            </td>
                                            <td class="border border-slate-200 px-4 py-3">
                                                <input type="number"
                                                       x-model="course.student_count"
                                                       x-bind:readonly="!course.editing"
                                                       x-bind:class="course.editing ? 'w-full px-2 py-1 border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500' : 'w-full bg-transparent border-none outline-none'"
                                                       placeholder="学生人数" min="1">
                                            </td>
                                            <td class="border border-slate-200 px-4 py-3 text-center">
                                                <div class="flex justify-center space-x-2">
                                                    <template x-if="!course.editing">
                                                        <div class="flex space-x-2">
                                                            <button type="button" x-on:click="editCourse(index)"
                                                                    class="text-blue-600 hover:text-blue-800 text-sm">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button type="button" x-on:click="deleteCourse(index)"
                                                                    class="text-red-600 hover:text-red-800 text-sm">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </template>
                                                    <template x-if="course.editing">
                                                        <div class="flex space-x-2">
                                                            <button type="button" x-on:click="saveCourse(index)"
                                                                    class="text-green-600 hover:text-green-800 text-sm">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                            <button type="button" x-on:click="cancelEdit(index)"
                                                                    class="text-gray-600 hover:text-gray-800 text-sm">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                                        </div>
                                                    </template>
                                                </div>
                                            </td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                        </div>

                        <!-- 添加更多课程按钮 -->
                        <div class="flex justify-end">
                            <button type="button" x-on:click="addNewCourse()"
                                    class="bg-green-500 hover:bg-green-600 text-white font-medium h-10 px-4 rounded-lg flex items-center space-x-2 text-sm transition-colors">
                                <i class="fas fa-plus"></i>
                                <span>添加更多主讲课程</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 邮寄地址管理卡片 (仅教师用户显示) -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 overflow-hidden" x-data="addressManager()" x-init="init()" x-show="showCard">
                <div class="p-6 border-b border-slate-100">
                    <h2 class="text-xl font-bold text-slate-800 flex items-center">
                        <i class="fas fa-map-marker-alt text-purple-500 mr-3"></i>
                        邮寄地址管理
                    </h2>
                    <p class="text-sm text-slate-600 mt-1">管理您的邮寄地址信息</p>
                </div>
                <div class="p-6">
                    <!-- 加载状态 -->
                    <div x-show="loading" class="flex items-center justify-center py-8">
                        <div class="flex items-center space-x-3">
                            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-500"></div>
                            <span class="text-slate-600">正在加载地址信息...</span>
                        </div>
                    </div>

                    <!-- 地址列表 -->
                    <div x-show="!loading" class="space-y-4">
                        <!-- 地址卡片列表 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <template x-for="address in addresses" :key="address.address_id">
                                <div class="border border-slate-200 rounded-xl p-4 hover:bg-slate-50 transition-colors">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <div class="flex items-center space-x-3 mb-2">
                                                <span class="font-medium text-slate-800" x-text="address.name"></span>
                                                <span class="text-slate-600 text-sm" x-text="address.phone_number"></span>
                                            </div>
                                            <p class="text-slate-600 text-sm leading-relaxed" x-text="getFullAddress(address)"></p>
                                        </div>
                                        <div class="flex space-x-2 ml-4">
                                            <button type="button" x-on:click="editAddress(address)"
                                                    class="text-blue-600 hover:text-blue-800 text-sm p-1 rounded hover:bg-blue-50 transition-colors"
                                                    title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" x-on:click="deleteAddress(address.address_id)"
                                                    class="text-red-600 hover:text-red-800 text-sm p-1 rounded hover:bg-red-50 transition-colors"
                                                    title="删除">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>

                        <!-- 无地址提示 -->
                        <div x-show="addresses.length === 0" class="text-center py-12">
                            <div class="text-slate-400 mb-4">
                                <i class="fas fa-map-marker-alt text-4xl"></i>
                            </div>
                            <p class="text-slate-500 mb-4">暂无邮寄地址</p>
                            <button type="button" x-on:click="addNewAddress()"
                                    class="bg-purple-500 hover:bg-purple-600 text-white font-medium h-10 px-4 rounded-lg flex items-center space-x-2 text-sm transition-colors mx-auto">
                                <i class="fas fa-plus"></i>
                                <span>添加第一个地址</span>
                            </button>
                        </div>

                        <!-- 添加地址按钮 -->
                        <div x-show="addresses.length > 0" class="flex justify-end pt-4">
                            <button type="button" x-on:click="addNewAddress()"
                                    class="bg-purple-500 hover:bg-purple-600 text-white font-medium h-10 px-4 rounded-lg flex items-center space-x-2 text-sm transition-colors">
                                <i class="fas fa-plus"></i>
                                <span>添加邮寄地址</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- 编辑个人信息模态框 -->
    <div id="editProfileModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
        <div class="bg-white rounded-2xl shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden">
            <div class="p-6 border-b border-slate-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-bold text-slate-800">编辑个人信息</h3>
                    <button type="button" onclick="closeEditProfileModal()" class="text-slate-400 hover:text-slate-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
                <form id="editProfileForm" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 姓名 -->
                        <div>
                            <label for="modal_name" class="block text-sm font-medium text-slate-700 mb-2">姓名 <span class="text-red-500">*</span></label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-id-card text-slate-400"></i>
                                </div>
                                <input type="text" id="modal_name" name="name"
                                       class="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="请输入姓名" required>
                            </div>
                        </div>

                        <!-- 手机号码 -->
                        <div>
                            <label for="modal_phone" class="block text-sm font-medium text-slate-700 mb-2">手机号码 <span class="text-red-500">*</span></label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-mobile-alt text-slate-400"></i>
                                </div>
                                <input type="tel" id="modal_phone" name="phone_number"
                                       class="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="请输入手机号码" required>
                            </div>
                        </div>
                    </div>

                    <!-- 邮箱 -->
                    <div>
                        <label for="modal_email" class="block text-sm font-medium text-slate-700 mb-2">邮箱 <span class="text-red-500">*</span></label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-envelope text-slate-400"></i>
                            </div>
                            <input type="email" id="modal_email" name="email"
                                   class="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="请输入邮箱地址" required>
                        </div>
                    </div>

                    <!-- 所属单位 (教师用户显示学校选择器) -->
                    <div id="schoolSelectContainer" style="display: none;">
                        <label class="block text-sm font-medium text-slate-700 mb-2">所属学校</label>
                        <div class="custom-select" id="modalSchoolSelect">
                            <div class="custom-select-trigger">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-building text-slate-400"></i>
                                </div>
                                <span class="custom-select-text pl-7">请选择学校</span>
                                <i class="fas fa-chevron-down custom-select-arrow"></i>
                            </div>
                            <div class="custom-select-dropdown">
                                <div class="custom-select-search">
                                    <input type="text" placeholder="搜索学校..." class="search-input">
                                </div>
                                <div class="custom-select-options" id="modalSchoolOptions">
                                    <!-- 选项将动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="p-6 border-t border-slate-200 flex justify-end space-x-4">
                <button type="button" onclick="closeEditProfileModal()"
                        class="px-6 py-3 bg-slate-500 text-white rounded-xl hover:bg-slate-600 transition-colors font-medium">
                    取消
                </button>
                <button type="button" onclick="saveProfileChanges()"
                        class="px-6 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors font-medium">
                    保存
                </button>
            </div>
        </div>
    </div>

    <!-- 修改用户名模态框 -->
    <div id="usernameModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
        <div class="bg-white rounded-2xl shadow-xl max-w-lg w-full mx-4">
            <div class="p-6 border-b border-slate-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-bold text-slate-800">修改用户名</h3>
                    <button type="button" onclick="closeUsernameModal()" class="text-slate-400 hover:text-slate-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <!-- 当前用户名 -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-slate-700 mb-2">当前用户名</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-user text-slate-400"></i>
                        </div>
                        <input type="text" id="currentUsername"
                               class="w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none text-slate-700"
                               readonly>
                    </div>
                </div>

                <!-- 权限检查结果 -->
                <div id="usernamePermissionInfo" class="mb-4"></div>

                <!-- 新用户名输入 -->
                <div id="newUsernameContainer" class="mb-4" style="display: none;">
                    <label for="newUsername" class="block text-sm font-medium text-slate-700 mb-2">新用户名 <span class="text-red-500">*</span></label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-user-edit text-slate-400"></i>
                        </div>
                        <input type="text" id="newUsername"
                               class="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                               placeholder="请输入新用户名" maxlength="50">
                    </div>
                    <p class="text-xs text-slate-500 mt-2">用户名长度3-50个字符，只能包含字母、数字、下划线和中文字符</p>
                </div>
            </div>
            <div class="p-6 border-t border-slate-200 flex justify-end space-x-4">
                <button type="button" onclick="closeUsernameModal()"
                        class="px-6 py-3 bg-slate-500 text-white rounded-xl hover:bg-slate-600 transition-colors font-medium">
                    取消
                </button>
                <button type="button" id="saveUsernameBtn" onclick="saveUsernameChanges()" style="display: none;"
                        class="px-6 py-3 bg-green-500 text-white rounded-xl hover:bg-green-600 transition-colors font-medium">
                    保存
                </button>
            </div>
        </div>
    </div>

    <!-- 修改密码模态框 -->
    <div id="passwordModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
        <div class="bg-white rounded-2xl shadow-xl max-w-lg w-full mx-4 max-h-[90vh] overflow-hidden">
            <div class="p-6 border-b border-slate-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-bold text-slate-800">修改密码</h3>
                    <button type="button" onclick="closePasswordModal()" class="text-slate-400 hover:text-slate-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
                <!-- 方式选择标签 -->
                <div class="flex bg-slate-100 rounded-lg p-1 mb-6">
                    <button type="button" onclick="switchPasswordMode('password')" id="passwordModeBtn"
                            class="flex-1 text-sm font-medium py-2 px-3 rounded-md transition-all bg-white text-purple-600 shadow-sm">
                        <i class="fas fa-lock mr-2"></i>旧密码验证
                    </button>
                    <button type="button" onclick="switchPasswordMode('email')" id="emailModeBtn"
                            class="flex-1 text-sm font-medium py-2 px-3 rounded-md transition-all text-slate-600 hover:text-slate-800">
                        <i class="fas fa-envelope mr-2"></i>邮箱验证码
                    </button>
                </div>

                <!-- 旧密码验证方式 -->
                <div id="passwordModeContent" class="space-y-6">
                    <form id="modalPasswordForm" class="space-y-6">
                        <!-- 当前密码 -->
                        <div>
                            <label for="modal_current_password" class="block text-sm font-medium text-slate-700 mb-2">
                                当前密码 <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-lock text-slate-400"></i>
                                </div>
                                <input type="password" id="modal_current_password" name="current_password"
                                       class="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                       placeholder="请输入当前密码" required>
                            </div>
                        </div>

                        <!-- 新密码 -->
                        <div>
                            <label for="modal_new_password" class="block text-sm font-medium text-slate-700 mb-2">
                                新密码 <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-unlock text-slate-400"></i>
                                </div>
                                <input type="password" id="modal_new_password" name="new_password"
                                       class="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                       placeholder="请输入新密码(至少6位)" required>
                            </div>
                            <p class="text-xs text-slate-500 mt-2">密码长度至少6个字符</p>
                        </div>

                        <!-- 确认新密码 -->
                        <div>
                            <label for="modal_confirm_password" class="block text-sm font-medium text-slate-700 mb-2">
                                确认新密码 <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-check-circle text-slate-400"></i>
                                </div>
                                <input type="password" id="modal_confirm_password" name="confirm_password"
                                       class="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                       placeholder="请再次输入新密码" required>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 邮箱验证码方式 -->
                <div id="emailModeContent" class="space-y-6" style="display: none;">
                    <form id="modalEmailPasswordForm" class="space-y-6">
                        <!-- 邮箱地址显示 -->
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">验证邮箱</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-envelope text-slate-400"></i>
                                </div>
                                <input type="email" id="modal_user_email"
                                       class="w-full pl-10 pr-4 py-3 border border-slate-300 bg-slate-50 rounded-xl focus:outline-none text-slate-700"
                                       readonly>
                            </div>
                            <p class="text-xs text-slate-500 mt-2">验证码将发送到此邮箱地址</p>
                        </div>

                        <!-- 验证码输入 -->
                        <div>
                            <label for="modal_email_code" class="block text-sm font-medium text-slate-700 mb-2">
                                邮箱验证码 <span class="text-red-500">*</span>
                            </label>
                            <div class="flex space-x-3">
                                <div class="relative flex-1">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-shield-alt text-slate-400"></i>
                                    </div>
                                    <input type="text" id="modal_email_code" name="email_verification_code"
                                           class="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                           placeholder="请输入6位验证码" maxlength="6" required>
                                </div>
                                <button type="button" id="modalSendEmailCodeBtn" onclick="sendEmailCode()"
                                        class="bg-green-500 hover:bg-green-600 text-white font-medium h-12 px-4 rounded-xl flex items-center space-x-2 shadow-lg whitespace-nowrap transition-colors">
                                    <i class="fas fa-paper-plane"></i>
                                    <span>发送验证码</span>
                                </button>
                            </div>
                        </div>

                        <!-- 新密码 -->
                        <div>
                            <label for="modal_email_new_password" class="block text-sm font-medium text-slate-700 mb-2">
                                新密码 <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-unlock text-slate-400"></i>
                                </div>
                                <input type="password" id="modal_email_new_password" name="email_new_password"
                                       class="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                       placeholder="请输入新密码(至少6位)" required>
                            </div>
                            <p class="text-xs text-slate-500 mt-2">密码长度至少6个字符</p>
                        </div>

                        <!-- 确认新密码 -->
                        <div>
                            <label for="modal_email_confirm_password" class="block text-sm font-medium text-slate-700 mb-2">
                                确认新密码 <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-check-circle text-slate-400"></i>
                                </div>
                                <input type="password" id="modal_email_confirm_password" name="email_confirm_password"
                                       class="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                       placeholder="请再次输入新密码" required>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="p-6 border-t border-slate-200 flex justify-end space-x-4">
                <button type="button" onclick="closePasswordModal()"
                        class="px-6 py-3 bg-slate-500 text-white rounded-xl hover:bg-slate-600 transition-colors font-medium">
                    取消
                </button>
                <button type="button" onclick="savePasswordChanges()"
                        class="px-6 py-3 bg-purple-500 text-white rounded-xl hover:bg-purple-600 transition-colors font-medium">
                    保存
                </button>
            </div>
        </div>
    </div>

    <!-- 地址编辑模态框 -->
    <div id="addressModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
        <div class="bg-white rounded-2xl shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden">
            <div class="p-6 border-b border-slate-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-bold text-slate-800" id="addressModalTitle">添加邮寄地址</h3>
                    <button type="button" onclick="closeAddressModal()" class="text-slate-400 hover:text-slate-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
                <form id="addressForm" class="space-y-6">
                    <input type="hidden" id="address_id" name="address_id">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 收件人姓名 -->
                        <div>
                            <label for="recipient_name" class="block text-sm font-medium text-slate-700 mb-2">收件人姓名 <span class="text-red-500">*</span></label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-user text-slate-400"></i>
                                </div>
                                <input type="text" id="recipient_name" name="name"
                                       class="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                       placeholder="请输入收件人姓名" required>
                            </div>
                        </div>

                        <!-- 联系电话 -->
                        <div>
                            <label for="recipient_phone" class="block text-sm font-medium text-slate-700 mb-2">联系电话 <span class="text-red-500">*</span></label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-phone text-slate-400"></i>
                                </div>
                                <input type="tel" id="recipient_phone" name="phone_number"
                                       class="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                       placeholder="请输入联系电话" required>
                            </div>
                        </div>
                    </div>

                    <!-- 地址选择 - 使用自定义搜索下拉框 -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">省份 <span class="text-red-500">*</span></label>
                            <div class="custom-select" id="provinceSelectContainer">
                                <!-- CustomSelect会动态生成内容 -->
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">城市 <span class="text-red-500">*</span></label>
                            <div class="custom-select" id="citySelectContainer">
                                <!-- CustomSelect会动态生成内容 -->
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">区县 <span class="text-red-500">*</span></label>
                            <div class="custom-select" id="districtSelectContainer">
                                <!-- CustomSelect会动态生成内容 -->
                            </div>
                        </div>
                    </div>

                    <!-- 详细地址 -->
                    <div>
                        <label for="detailed_address" class="block text-sm font-medium text-slate-700 mb-2">详细地址 <span class="text-red-500">*</span></label>
                        <div class="relative">
                            <div class="absolute top-3 left-3 pointer-events-none">
                                <i class="fas fa-map-marker-alt text-slate-400"></i>
                            </div>
                            <textarea id="detailed_address" name="detailed_address" rows="3"
                                      class="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                                      placeholder="请输入详细地址，如街道、门牌号等" required></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="p-6 border-t border-slate-200 flex justify-end space-x-4">
                <button type="button" onclick="closeAddressModal()"
                        class="px-6 py-3 bg-slate-500 text-white rounded-xl hover:bg-slate-600 transition-colors font-medium">
                    取消
                </button>
                <button type="button" onclick="saveAddressChanges()"
                        class="px-6 py-3 bg-purple-500 text-white rounded-xl hover:bg-purple-600 transition-colors font-medium">
                    保存
                </button>
            </div>
        </div>
    </div>



    <script>
        let messageId = 0;

        // Alpine.js 个人资料管理组件
        function profileManager() {
            return {
                userRole: '',
                organizationName: '',
                formData: {
                    username: '',
                    name: '',
                    phone_number: '',
                    email: ''
                },
                loading: true,
                isEditing: false,
                originalData: {},
                schoolSelect: null,
                schools: [],
                selectedSchoolId: '',

                init() {
                    // 从API获取用户信息
                    this.loadUserInfo();
                },

                async loadUserInfo() {
                    try {
                        const response = await fetch('/api/common/get_user_info');
                        const data = await response.json();

                        if (data.status === 'success') {
                            const userInfo = data.user_info;
                            this.userRole = userInfo.role;
                            this.formData = {
                                username: userInfo.username || '',
                                name: userInfo.name || '',
                                phone_number: userInfo.phone_number || '',
                                email: userInfo.email || ''
                            };

                            // 获取组织名称和ID
                            if (userInfo.role === 'teacher') {
                                this.organizationName = userInfo.school_name || '未设置';
                                this.selectedSchoolId = userInfo.school_id || '';
                                // 如果是教师用户，加载学校列表
                                await this.loadSchools();
                            } else if (userInfo.role === 'publisher' && userInfo.company_name) {
                                this.organizationName = userInfo.company_name;
                            } else if (userInfo.role === 'dealer' && userInfo.company_name) {
                                this.organizationName = userInfo.company_name;
                            }

                            // 保存原始数据
                            this.originalData = JSON.parse(JSON.stringify(this.formData));
                            this.loading = false;
                        } else {
                            showMessage('获取用户信息失败', 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                },

                async loadSchools() {
                    try {
                        const response = await fetch('/api/common/get_schools');
                        const data = await response.json();

                        if (data.code === 0) {
                            this.schools = data.data.map(school => ({
                                value: school.id.toString(),
                                text: school.name
                            }));
                        }
                    } catch (error) {
                        console.error('加载学校列表失败:', error);
                    }
                },

                startEditing() {
                    this.isEditing = true;
                    this.originalData = JSON.parse(JSON.stringify(this.formData));

                    // 如果是教师用户，初始化学校选择器
                    if (this.userRole === 'teacher') {
                        setTimeout(() => {
                            this.initSchoolSelect();
                        }, 100);
                    }
                },

                initSchoolSelect() {
                    // 销毁之前的实例
                    if (this.schoolSelect) {
                        this.schoolSelect.destroy();
                    }

                    // 创建新的学校选择器
                    this.schoolSelect = new CustomSelect('schoolSelectContainer', {
                        placeholder: '请选择学校',
                        searchPlaceholder: '搜索学校...',
                        onSelect: (value, text) => {
                            this.selectedSchoolId = value;
                            this.organizationName = text;
                        }
                    });

                    // 设置选项
                    this.schoolSelect.setOptions(this.schools);

                    // 设置当前值
                    if (this.selectedSchoolId) {
                        this.schoolSelect.setValue(this.selectedSchoolId);
                    }
                },

                cancelEditing() {
                    this.isEditing = false;
                    this.formData = JSON.parse(JSON.stringify(this.originalData));

                    // 销毁学校选择器
                    if (this.schoolSelect) {
                        this.schoolSelect.destroy();
                        this.schoolSelect = null;
                    }
                },

                async saveProfileChanges() {
                    // 表单验证
                    if (!this.formData.name) {
                        showMessage('姓名不能为空', 'error');
                        return;
                    }

                    if (!this.formData.phone_number) {
                        showMessage('手机号码不能为空', 'error');
                        return;
                    }

                    // 验证手机号格式
                    const phoneRegex = /^1[3-9]\d{9}$/;
                    if (!phoneRegex.test(this.formData.phone_number)) {
                        showMessage('请输入正确的手机号码', 'error');
                        return;
                    }

                    if (!this.formData.email) {
                        showMessage('邮箱不能为空', 'error');
                        return;
                    }

                    // 验证邮箱格式
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(this.formData.email)) {
                        showMessage('请输入正确的邮箱格式', 'error');
                        return;
                    }

                    try {
                        const submitData = {
                            name: this.formData.name,
                            phone_number: this.formData.phone_number,
                            email: this.formData.email
                        };

                        // 如果是教师用户且选择了学校，添加学校ID
                        if (this.userRole === 'teacher' && this.selectedSchoolId) {
                            submitData.school_id = this.selectedSchoolId;
                        }

                        const response = await fetch('/api/common/edit_profile', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(submitData)
                        });

                        const data = await response.json();

                        if (data.code === 0) {
                            showMessage('个人信息更新成功', 'success');
                            this.isEditing = false;

                            // 销毁学校选择器
                            if (this.schoolSelect) {
                                this.schoolSelect.destroy();
                                this.schoolSelect = null;
                            }

                            // 重新加载用户信息以更新组织名称
                            await this.loadUserInfo();

                            // 延迟1.5秒后刷新页面，让用户看到成功提示
                            setTimeout(() => {
                                location.reload();
                            }, 1500);
                        } else {
                            showMessage(data.message || '更新失败', 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                },

                openEditProfileModal() {
                    openEditProfileModal();
                },

                openUsernameModal() {
                    openUsernameModal();
                },

                openPasswordModal() {
                    openPasswordModal();
                },

                getRoleDisplayName(role) {
                    const roleMap = {
                        'teacher': '教师',
                        'publisher': '出版社',
                        'dealer': '经销商',
                        'admin': '管理员'
                    };
                    return roleMap[role] || '未知';
                }
            }
        }

        // 课程管理组件
        function courseManager() {
            return {
                loading: true,
                showCard: false,
                courses: [],
                userRole: '',

                init() {
                    this.checkUserRole().then(() => {
                        if (this.userRole === 'teacher') {
                            this.loadCourses();
                        } else {
                            this.loading = false;
                        }
                    });
                },

                async checkUserRole() {
                    try {
                        const response = await fetch('/api/common/get_user_info');
                        const data = await response.json();

                        if (data.status === 'success') {
                            this.userRole = data.user_info.role;
                            this.showCard = (this.userRole === 'teacher');
                        }
                    } catch (error) {
                        console.error('检查用户角色失败:', error);
                    }
                },

                async loadCourses() {
                    try {
                        const response = await fetch('/api/teacher/get_teacher_courses');
                        const data = await response.json();

                        if (data.code === 0) {
                            this.courses = data.data.map(course => ({
                                ...course,
                                editing: false,
                                originalData: { ...course }
                            }));
                        } else {
                            showMessage('加载课程失败', 'error');
                        }
                        this.loading = false;
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                        this.loading = false;
                    }
                },

                addNewCourse() {
                    this.courses.push({
                        id: null,
                        course_name: '',
                        semester: '',
                        course_type: '',
                        student_count: '',
                        editing: true,
                        isNew: true
                    });
                },

                editCourse(index) {
                    this.courses[index].editing = true;
                    this.courses[index].originalData = { ...this.courses[index] };
                },

                cancelEdit(index) {
                    if (this.courses[index].isNew) {
                        this.courses.splice(index, 1);
                    } else {
                        Object.assign(this.courses[index], this.courses[index].originalData);
                        this.courses[index].editing = false;
                    }
                },

                async saveCourse(index) {
                    const course = this.courses[index];

                    // 验证必填字段
                    if (!course.course_name || !course.semester || !course.course_type || !course.student_count) {
                        showMessage('请填写完整的课程信息', 'error');
                        return;
                    }

                    if (course.student_count <= 0) {
                        showMessage('学生人数必须大于0', 'error');
                        return;
                    }

                    try {
                        const formData = new FormData();
                        formData.append('course_name', course.course_name);
                        formData.append('semester', course.semester);
                        formData.append('course_type', course.course_type);
                        formData.append('student_count', course.student_count);

                        let response;
                        if (course.isNew) {
                            response = await fetch('/api/teacher/add_teacher_course', {
                                method: 'POST',
                                body: formData
                            });
                        } else {
                            formData.append('id', course.id);
                            response = await fetch('/api/teacher/edit_teacher_course', {
                                method: 'POST',
                                body: formData
                            });
                        }

                        const data = await response.json();

                        if (data.code === 0) {
                            if (course.isNew) {
                                course.id = data.course_id;
                                course.isNew = false;
                            }
                            course.editing = false;
                            showMessage('课程保存成功', 'success');
                        } else {
                            showMessage(data.message || '保存失败', 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                },

                async deleteCourse(index) {
                    const course = this.courses[index];

                    if (course.isNew) {
                        this.courses.splice(index, 1);
                        return;
                    }

                    if (!confirm('确定要删除这门课程吗？')) {
                        return;
                    }

                    try {
                        const formData = new FormData();
                        formData.append('course_id', course.id);

                        const response = await fetch('/api/teacher/delete_teacher_course', {
                            method: 'POST',
                            body: formData
                        });

                        const data = await response.json();

                        if (data.code === 0) {
                            this.courses.splice(index, 1);
                            showMessage('课程删除成功', 'success');
                        } else {
                            showMessage(data.message || '删除失败', 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                }
            }
        }

        // Alpine.js 教师信息管理组件
        function teacherInfoManager() {
            return {
                isEditing: false,
                loading: true,
                showCard: false,
                userRole: '',
                genderSelect: null,
                formData: {
                    department: '',
                    position: '',
                    title: '',
                    gender: ''
                },
                originalData: {},

                init() {
                    this.checkUserRole().then(() => {
                        if (this.userRole === 'teacher') {
                            this.loadTeacherInfo();
                        } else {
                            this.loading = false;
                        }
                    });
                },

                async checkUserRole() {
                    try {
                        const response = await fetch('/api/common/get_user_info');
                        const data = await response.json();

                        if (data.status === 'success') {
                            this.userRole = data.user_info.role;
                            this.showCard = (this.userRole === 'teacher');
                        } else {
                            this.showCard = false;
                        }
                    } catch (error) {
                        console.error('获取用户角色失败:', error);
                        this.showCard = false;
                    }
                },

                async loadTeacherInfo() {
                    try {
                        const response = await fetch('/api/common/get_teacher_info');
                        const data = await response.json();

                        if (data.status === 'success') {
                            const teacherInfo = data.teacher_info || {};
                            this.formData = {
                                department: teacherInfo.department || '',
                                position: teacherInfo.position || '',
                                title: teacherInfo.title || '',
                                gender: teacherInfo.gender || ''
                            };

                            // 保存原始数据
                            this.originalData = JSON.parse(JSON.stringify(this.formData));
                            this.loading = false;
                        } else {
                            // 如果没有教师信息记录，创建空记录
                            this.formData = {
                                department: '',
                                position: '',
                                title: '',
                                gender: ''
                            };
                            this.originalData = JSON.parse(JSON.stringify(this.formData));
                            this.loading = false;
                        }
                    } catch (error) {
                        showMessage('加载教师信息失败', 'error');
                        this.loading = false;
                    }
                },

                startEditing() {
                    this.isEditing = true;
                    this.originalData = JSON.parse(JSON.stringify(this.formData));

                    // 如果有性别选择器，初始化它
                    setTimeout(() => {
                        this.initGenderSelect();
                    }, 100);
                },

                cancelEditing() {
                    this.isEditing = false;
                    this.formData = JSON.parse(JSON.stringify(this.originalData));

                    // 重置性别选择器显示
                    const textSpan = document.querySelector('#genderSelectContainer .custom-select-text');
                    const dropdown = document.querySelector('#genderSelectContainer .custom-select-dropdown');
                    if (textSpan) {
                        textSpan.textContent = this.formData.gender || '请选择性别';
                    }
                    if (dropdown) {
                        dropdown.style.display = 'none';
                    }
                },

                initGenderSelect() {
                    // 由于HTML中已经有静态的性别选择器结构，我们需要手动初始化
                    const container = document.getElementById('genderSelectContainer');
                    const optionsContainer = document.getElementById('genderSelectOptions');
                    const trigger = container?.querySelector('.custom-select-trigger');
                    const textSpan = container?.querySelector('.custom-select-text');
                    const dropdown = container?.querySelector('.custom-select-dropdown');

                    if (container && optionsContainer && trigger && textSpan && dropdown) {
                        // 清空并添加选项
                        optionsContainer.innerHTML = `
                            <div class="custom-select-option" data-value="男">男</div>
                            <div class="custom-select-option" data-value="女">女</div>
                        `;

                        // 设置当前值
                        if (this.formData.gender) {
                            textSpan.textContent = this.formData.gender;
                        }

                        // 绑定点击事件
                        trigger.addEventListener('click', () => {
                            dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
                        });

                        // 绑定选项点击事件
                        optionsContainer.addEventListener('click', (e) => {
                            if (e.target.classList.contains('custom-select-option')) {
                                const value = e.target.getAttribute('data-value');
                                this.formData.gender = value;
                                textSpan.textContent = value;
                                dropdown.style.display = 'none';
                            }
                        });

                        // 点击外部关闭下拉框
                        document.addEventListener('click', (e) => {
                            if (!container.contains(e.target)) {
                                dropdown.style.display = 'none';
                            }
                        });
                    }
                },

                async saveTeacherInfo() {
                    // 表单验证
                    if (!this.formData.department) {
                        showMessage('请输入院系', 'error');
                        return;
                    }

                    if (!this.formData.position) {
                        showMessage('请输入职务', 'error');
                        return;
                    }

                    if (!this.formData.title) {
                        showMessage('请输入职称', 'error');
                        return;
                    }

                    if (!this.formData.gender) {
                        showMessage('请选择性别', 'error');
                        return;
                    }

                    try {
                        const response = await fetch('/api/teacher/update_teacher_info', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                department: this.formData.department,
                                position: this.formData.position,
                                title: this.formData.title,
                                gender: this.formData.gender
                            })
                        });

                        const data = await response.json();

                        if (data.code === 0) {
                            showMessage('教师信息更新成功', 'success');
                            this.isEditing = false;
                            this.originalData = JSON.parse(JSON.stringify(this.formData));

                            // 延迟1.5秒后刷新页面，让用户看到成功提示
                            setTimeout(() => {
                                location.reload();
                            }, 1500);
                        } else {
                            showMessage(data.message || '更新失败', 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                }
            }
        }

        // 地址管理组件
        function addressManager() {
            return {
                loading: true,
                showCard: false,
                addresses: [],
                userRole: '',
                addressData: null,
                provinceSelect: null,
                citySelect: null,
                districtSelect: null,

                init() {
                    this.checkUserRole().then(() => {
                        if (this.userRole === 'teacher') {
                            Promise.all([this.loadAddresses(), this.loadAddressData()]);
                        } else {
                            this.loading = false;
                        }
                    });
                },

                async checkUserRole() {
                    try {
                        const response = await fetch('/api/common/get_user_info');
                        const data = await response.json();

                        if (data.status === 'success') {
                            this.userRole = data.user_info.role;
                            this.showCard = (this.userRole === 'teacher');
                        }
                    } catch (error) {
                        console.error('检查用户角色失败:', error);
                    }
                },

                async loadAddresses() {
                    try {
                        const response = await fetch('/api/teacher/get_addresses?page=1&limit=100');
                        const data = await response.json();

                        if (data.code === 0) {
                            this.addresses = data.data;
                        } else {
                            showMessage('加载地址失败', 'error');
                        }
                        this.loading = false;
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                        this.loading = false;
                    }
                },

                async loadAddressData() {
                    try {
                        const response = await fetch('/api/teacher/get_address_data');
                        const data = await response.json();

                        if (data.code === 0) {
                            // 将对象转换为数组格式，并转换字段名
                            this.addressData = Object.values(data.data).map(province => ({
                                name: province.n,
                                children: province.c ? Object.values(province.c).map(city => ({
                                    name: city.n,
                                    children: city.c ? Object.values(city.c).map(district => ({
                                        name: district.n
                                    })) : []
                                })) : []
                            }));
                        }
                    } catch (error) {
                        console.error('加载地址数据失败:', error);
                    }
                },

                getFullAddress(address) {
                    if (!address) return '';
                    const parts = [address.province, address.city, address.district, address.detailed_address].filter(part => part);
                    return parts.join('');
                },

                addNewAddress() {
                    openAddressModal();
                },

                editAddress(address) {
                    openAddressModal(address);
                },

                async deleteAddress(addressId) {
                    if (!confirm('确定要删除这个地址吗？')) {
                        return;
                    }

                    try {
                        const formData = new FormData();
                        formData.append('address_id', addressId);

                        const response = await fetch('/api/teacher/new_delete_address', {
                            method: 'POST',
                            body: formData
                        });

                        const data = await response.json();

                        if (data.code === 0) {
                            showMessage('地址删除成功', 'success');

                            // 延迟1.5秒后刷新页面，让用户看到成功提示
                            setTimeout(() => {
                                location.reload();
                            }, 1500);
                        } else {
                            showMessage(data.message || '删除失败', 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                }
            }
        }

        // 模态框管理函数
        let currentUserData = null;
        let modalSchoolSelect = null;
        let emailCodeCooldown = 0;
        let emailCodeTimer = null;

        // 打开编辑个人信息模态框
        async function openEditProfileModal() {
            try {
                const response = await fetch('/api/common/get_user_info');
                const data = await response.json();

                if (data.status === 'success') {
                    currentUserData = data.user_info;

                    // 填充表单数据
                    document.getElementById('modal_name').value = currentUserData.name || '';
                    document.getElementById('modal_phone').value = currentUserData.phone_number || '';
                    document.getElementById('modal_email').value = currentUserData.email || '';

                    // 如果是教师用户，显示学校选择器
                    if (currentUserData.role === 'teacher') {
                        document.getElementById('schoolSelectContainer').style.display = 'block';
                        await initModalSchoolSelect();
                    } else {
                        document.getElementById('schoolSelectContainer').style.display = 'none';
                    }

                    document.getElementById('editProfileModal').classList.remove('hidden');
                } else {
                    showMessage('获取用户信息失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误，请稍后重试', 'error');
            }
        }

        // 关闭编辑个人信息模态框
        function closeEditProfileModal() {
            document.getElementById('editProfileModal').classList.add('hidden');
            if (modalSchoolSelect) {
                modalSchoolSelect.destroy();
                modalSchoolSelect = null;
            }
        }

        // 保存个人信息更改
        async function saveProfileChanges() {
            const name = document.getElementById('modal_name').value.trim();
            const phone = document.getElementById('modal_phone').value.trim();
            const email = document.getElementById('modal_email').value.trim();

            // 表单验证
            if (!name) {
                showMessage('姓名不能为空', 'error');
                return;
            }

            if (!phone) {
                showMessage('手机号码不能为空', 'error');
                return;
            }

            // 验证手机号格式
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(phone)) {
                showMessage('请输入正确的手机号码', 'error');
                return;
            }

            if (!email) {
                showMessage('邮箱不能为空', 'error');
                return;
            }

            // 验证邮箱格式
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showMessage('请输入正确的邮箱格式', 'error');
                return;
            }

            try {
                const submitData = {
                    name: name,
                    phone_number: phone,
                    email: email
                };

                // 如果是教师用户且选择了学校，添加学校ID
                if (currentUserData.role === 'teacher' && modalSchoolSelect) {
                    const schoolId = modalSchoolSelect.getValue();
                    if (schoolId) {
                        submitData.school_id = schoolId;
                    }
                }

                const response = await fetch('/api/common/edit_profile', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(submitData)
                });

                const data = await response.json();

                if (data.code === 0) {
                    showMessage('个人信息更新成功！', 'success');
                    closeEditProfileModal();

                    // 延迟1.5秒后刷新页面，让用户看到成功提示
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showMessage(data.message || '个人信息更新失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误，请稍后重试', 'error');
            }
        }

        // 初始化模态框中的学校选择器
        async function initModalSchoolSelect() {
            try {
                // 加载学校数据
                const response = await fetch('/api/common/get_schools');
                const data = await response.json();

                if (data.code === 0) {
                    // 这里可以实现学校选择器的初始化
                    // 由于CustomSelect类在原代码中比较复杂，这里简化处理
                    const schoolOptions = data.data;
                    const selectElement = document.querySelector('#modalSchoolSelect .custom-select-options');
                    if (selectElement) {
                        selectElement.innerHTML = schoolOptions.map(school =>
                            `<div class="custom-select-option" data-value="${school.id}">${school.name}</div>`
                        ).join('');
                    }
                } else {
                    showMessage('加载学校列表失败', 'error');
                }
            } catch (error) {
                showMessage('加载学校列表失败', 'error');
            }
        }

        // 打开修改用户名模态框
        async function openUsernameModal() {
            try {
                // 获取当前用户名
                const response = await fetch('/api/common/get_user_info');
                const data = await response.json();

                if (data.status === 'success') {
                    document.getElementById('currentUsername').value = data.user_info.username || '';

                    // 检查修改权限
                    await checkUsernamePermission();

                    document.getElementById('usernameModal').classList.remove('hidden');
                } else {
                    showMessage('获取用户信息失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误，请稍后重试', 'error');
            }
        }

        // 关闭修改用户名模态框
        function closeUsernameModal() {
            document.getElementById('usernameModal').classList.add('hidden');
            document.getElementById('newUsernameContainer').style.display = 'none';
            document.getElementById('saveUsernameBtn').style.display = 'none';
            document.getElementById('newUsername').value = '';
        }

        // 检查用户名修改权限
        async function checkUsernamePermission() {
            try {
                const response = await fetch('/api/common/check_username_change_permission');
                const data = await response.json();

                if (data.status === 'success') {
                    const canChange = data.can_change;
                    const nextChangeTime = data.next_change_time;

                    const infoDiv = document.getElementById('usernamePermissionInfo');
                    if (canChange) {
                        infoDiv.innerHTML = `
                            <p class="text-sm text-green-700">
                                <i class="fas fa-check-circle text-green-500 mr-1"></i>
                                您可以修改用户名，修改后半年内不可再次修改
                            </p>
                        `;
                        document.getElementById('newUsernameContainer').style.display = 'block';
                        document.getElementById('saveUsernameBtn').style.display = 'inline-block';
                    } else {
                        infoDiv.innerHTML = `
                            <p class="text-sm text-slate-400">
                                <i class="fas fa-clock text-slate-400 mr-1"></i>
                                ${nextChangeTime ? `下次可修改时间：${nextChangeTime}` : '暂时无法修改用户名'}
                            </p>
                        `;
                        document.getElementById('newUsernameContainer').style.display = 'none';
                        document.getElementById('saveUsernameBtn').style.display = 'none';
                    }
                } else {
                    showMessage(data.message || '检查权限失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误，请稍后重试', 'error');
            }
        }

        // 保存用户名更改
        async function saveUsernameChanges() {
            const newUsername = document.getElementById('newUsername').value.trim();

            // 表单验证
            if (!newUsername) {
                showMessage('请输入新用户名', 'error');
                return;
            }

            if (newUsername.length < 3 || newUsername.length > 50) {
                showMessage('用户名长度必须在3-50个字符之间', 'error');
                return;
            }

            // 验证用户名格式
            const usernameRegex = /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/;
            if (!usernameRegex.test(newUsername)) {
                showMessage('用户名只能包含字母、数字、下划线和中文字符', 'error');
                return;
            }

            // 确认修改
            if (!confirm('确定要修改用户名吗？修改后半年内不可再次修改。')) {
                return;
            }

            try {
                const response = await fetch('/api/common/change_username', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        new_username: newUsername
                    })
                });

                const data = await response.json();

                if (data.status === 'success') {
                    showMessage('用户名修改成功！', 'success');
                    closeUsernameModal();
                    // 刷新页面以更新显示的当前用户名
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    showMessage(data.message || '用户名修改失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误，请稍后重试', 'error');
            }
        }

        // 打开修改密码模态框
        function openPasswordModal() {
            // 获取用户邮箱并填充
            fetch('/api/common/get_user_info')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        document.getElementById('modal_user_email').value = data.user_info.email || '';
                    }
                });

            document.getElementById('passwordModal').classList.remove('hidden');
        }

        // 关闭修改密码模态框
        function closePasswordModal() {
            document.getElementById('passwordModal').classList.add('hidden');
            // 清空表单
            document.getElementById('modalPasswordForm').reset();
            document.getElementById('modalEmailPasswordForm').reset();
            // 重置模式
            switchPasswordMode('password');
        }

        // 切换密码修改模式
        function switchPasswordMode(mode) {
            const passwordModeBtn = document.getElementById('passwordModeBtn');
            const emailModeBtn = document.getElementById('emailModeBtn');
            const passwordContent = document.getElementById('passwordModeContent');
            const emailContent = document.getElementById('emailModeContent');

            if (mode === 'password') {
                passwordModeBtn.className = 'flex-1 text-sm font-medium py-2 px-3 rounded-md transition-all bg-white text-purple-600 shadow-sm';
                emailModeBtn.className = 'flex-1 text-sm font-medium py-2 px-3 rounded-md transition-all text-slate-600 hover:text-slate-800';
                passwordContent.style.display = 'block';
                emailContent.style.display = 'none';
            } else {
                passwordModeBtn.className = 'flex-1 text-sm font-medium py-2 px-3 rounded-md transition-all text-slate-600 hover:text-slate-800';
                emailModeBtn.className = 'flex-1 text-sm font-medium py-2 px-3 rounded-md transition-all bg-white text-purple-600 shadow-sm';
                passwordContent.style.display = 'none';
                emailContent.style.display = 'block';
            }
        }

        // 保存密码更改
        async function savePasswordChanges() {
            const passwordContent = document.getElementById('passwordModeContent');
            const emailContent = document.getElementById('emailModeContent');

            if (passwordContent.style.display !== 'none') {
                // 旧密码验证方式
                const currentPassword = document.getElementById('modal_current_password').value;
                const newPassword = document.getElementById('modal_new_password').value;
                const confirmPassword = document.getElementById('modal_confirm_password').value;

                // 表单验证
                if (!currentPassword) {
                    showMessage('请输入当前密码', 'error');
                    return;
                }

                if (!newPassword) {
                    showMessage('请输入新密码', 'error');
                    return;
                }

                if (newPassword.length < 6) {
                    showMessage('新密码长度至少6个字符', 'error');
                    return;
                }

                if (newPassword !== confirmPassword) {
                    showMessage('两次输入的新密码不一致', 'error');
                    return;
                }

                try {
                    const response = await fetch('/api/common/change_password', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            current_password: currentPassword,
                            new_password: newPassword,
                            confirm_password: confirmPassword
                        })
                    });

                    const data = await response.json();

                    if (data.code === 0) {
                        showMessage('密码修改成功！', 'success');
                        closePasswordModal();
                    } else {
                        showMessage(data.message || '密码修改失败', 'error');
                    }
                } catch (error) {
                    showMessage('网络错误，请稍后重试', 'error');
                }
            } else {
                // 邮箱验证码方式
                const email = document.getElementById('modal_user_email').value;
                const code = document.getElementById('modal_email_code').value;
                const newPassword = document.getElementById('modal_email_new_password').value;
                const confirmPassword = document.getElementById('modal_email_confirm_password').value;

                // 表单验证
                if (!email) {
                    showMessage('请先设置并保存邮箱地址', 'error');
                    return;
                }

                if (!code) {
                    showMessage('请输入邮箱验证码', 'error');
                    return;
                }

                if (code.length !== 6) {
                    showMessage('验证码应为6位数字', 'error');
                    return;
                }

                if (!newPassword) {
                    showMessage('请输入新密码', 'error');
                    return;
                }

                if (newPassword.length < 6) {
                    showMessage('新密码长度至少6个字符', 'error');
                    return;
                }

                if (newPassword !== confirmPassword) {
                    showMessage('两次输入的新密码不一致', 'error');
                    return;
                }

                try {
                    const response = await fetch('/api/common/reset_password', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            email: email,
                            code: code,
                            new_password: newPassword
                        })
                    });

                    const data = await response.json();

                    if (data.code === 0) {
                        showMessage('密码修改成功！', 'success');
                        closePasswordModal();
                    } else {
                        showMessage(data.message || '密码修改失败', 'error');
                    }
                } catch (error) {
                    showMessage('网络错误，请稍后重试', 'error');
                }
            }
        }

        // 发送邮箱验证码
        async function sendEmailCode() {
            const email = document.getElementById('modal_user_email').value;

            if (!email) {
                showMessage('请先设置邮箱地址', 'error');
                return;
            }

            try {
                const response = await fetch('/api/common/send_verification_code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        purpose: 'reset_password'
                    })
                });

                const data = await response.json();

                if (data.code === 0) {
                    showMessage('验证码已发送到您的邮箱', 'success');
                    startEmailCodeCooldown();
                } else {
                    showMessage(data.message || '发送验证码失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误，请稍后重试', 'error');
            }
        }

        // 开始邮箱验证码倒计时
        function startEmailCodeCooldown() {
            emailCodeCooldown = 60;
            const btn = document.getElementById('modalSendEmailCodeBtn');

            const timer = setInterval(() => {
                if (emailCodeCooldown > 0) {
                    btn.disabled = true;
                    btn.innerHTML = `<i class="fas fa-paper-plane"></i><span>${emailCodeCooldown}s</span>`;
                    emailCodeCooldown--;
                } else {
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-paper-plane"></i><span>发送验证码</span>';
                    clearInterval(timer);
                }
            }, 1000);
        }











        // 消息通知函数 - 按照设计规范实现
        function showMessage(text, type = 'info') {
            const id = ++messageId;
            const container = document.getElementById('messageContainer');
            
            const messageEl = document.createElement('div');
            messageEl.id = `message-${id}`;
            messageEl.className = `max-w-sm w-full bg-white border-l-4 rounded-xl shadow-lg p-4 transform transition-all duration-300 translate-x-full opacity-0 ${
                type === 'success' ? 'border-green-500' : 
                type === 'error' ? 'border-red-500' : 
                type === 'warning' ? 'border-yellow-500' : 'border-blue-500'
            }`;
            
            messageEl.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-5 h-5 mr-3 ${
                        type === 'success' ? 'text-green-500' : 
                        type === 'error' ? 'text-red-500' : 
                        type === 'warning' ? 'text-yellow-500' : 'text-blue-500'
                    }">
                        <i class="fas ${
                            type === 'success' ? 'fa-check-circle' : 
                            type === 'error' ? 'fa-exclamation-circle' : 
                            type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'
                        }"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-slate-800">${text}</p>
                    </div>
                    <button onclick="removeMessage(${id})" 
                            class="flex-shrink-0 ml-3 text-slate-400 hover:text-slate-600 transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            `;
            
            container.appendChild(messageEl);
            
            // 动画显示
            setTimeout(() => {
                messageEl.classList.remove('translate-x-full', 'opacity-0');
            }, 10);
            
            // 5秒后自动移除
            setTimeout(() => {
                removeMessage(id);
            }, 5000);
        }
        
        // 移除消息
        function removeMessage(id) {
            const messageEl = document.getElementById(`message-${id}`);
            if (messageEl) {
                messageEl.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    messageEl.remove();
                }, 300);
            }
        }
        // CustomSelect类 - 简化版本用于学校选择
        class CustomSelect {
            constructor(containerId, options = {}) {
                this.container = document.getElementById(containerId);
                if (!this.container) {
                    console.error('CustomSelect: 容器元素未找到:', containerId);
                    return;
                }

                this.options = [];
                this.selectedValue = '';
                this.selectedText = '';
                this.placeholder = options.placeholder || '请选择';
                this.searchPlaceholder = options.searchPlaceholder || '搜索...';
                this.onSelect = options.onSelect || null;

                this.init();
            }

            init() {
                // 创建HTML结构
                this.container.innerHTML = `
                    <div class="custom-select-trigger">
                        <span class="custom-select-text">${this.placeholder}</span>
                        <i class="fas fa-chevron-down custom-select-arrow"></i>
                    </div>
                    <div class="custom-select-dropdown">
                        <div class="custom-select-search">
                            <input type="text" placeholder="${this.searchPlaceholder}" class="search-input">
                        </div>
                        <div class="custom-select-options"></div>
                    </div>
                `;

                this.trigger = this.container.querySelector('.custom-select-trigger');
                this.dropdown = this.container.querySelector('.custom-select-dropdown');
                this.searchInput = this.container.querySelector('.search-input');
                this.optionsContainer = this.container.querySelector('.custom-select-options');
                this.textSpan = this.container.querySelector('.custom-select-text');

                this.bindEvents();
            }

            bindEvents() {
                // 点击触发器
                this.trigger.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.toggle();
                });

                // 搜索功能
                this.searchInput.addEventListener('input', (e) => {
                    this.filterOptions(e.target.value);
                });

                // 点击选项
                this.optionsContainer.addEventListener('click', (e) => {
                    if (e.target.classList.contains('custom-select-option') && !e.target.classList.contains('no-results')) {
                        const value = e.target.dataset.value;
                        const text = e.target.textContent;
                        this.selectOption(value, text);
                    }
                });

                // 点击外部关闭
                document.addEventListener('click', (e) => {
                    if (!this.container.contains(e.target)) {
                        this.close();
                    }
                });
            }

            setOptions(options) {
                this.options = options;
                this.renderOptions();
            }

            setValue(value) {
                const option = this.options.find(opt => opt.value === value);
                if (option) {
                    this.selectOption(value, option.text);
                }
            }

            getValue() {
                return this.selectedValue;
            }

            toggle() {
                if (this.container.classList.contains('active')) {
                    this.close();
                } else {
                    this.open();
                }
            }

            open() {
                this.container.classList.add('active');
                this.searchInput.focus();
            }

            close() {
                this.container.classList.remove('active');
                this.searchInput.value = '';
                this.renderOptions();
            }

            selectOption(value, text) {
                this.selectedValue = value;
                this.selectedText = text;
                this.textSpan.textContent = text;
                this.close();

                if (this.onSelect) {
                    this.onSelect(value, text);
                }
            }

            filterOptions(searchTerm) {
                const filteredOptions = this.options.filter(option =>
                    option.text.toLowerCase().includes(searchTerm.toLowerCase())
                );
                this.renderOptions(filteredOptions);
            }

            renderOptions(optionsToRender = null) {
                const options = optionsToRender || this.options;

                if (options.length === 0) {
                    this.optionsContainer.innerHTML = '<div class="custom-select-option no-results">无匹配结果</div>';
                } else {
                    this.optionsContainer.innerHTML = options.map(option =>
                        `<div class="custom-select-option ${option.value === this.selectedValue ? 'selected' : ''}" data-value="${option.value}">${option.text}</div>`
                    ).join('');
                }
            }

            destroy() {
                if (this.container) {
                    this.container.classList.remove('active');
                    this.container.innerHTML = '';
                }
                this.selectedValue = '';
                this.selectedText = '';
                this.options = [];
            }
        }

        // 地址管理相关函数
        let currentAddressData = null;
        let addressFormData = null;
        let provinceSelect = null;
        let citySelect = null;
        let districtSelect = null;

        // 打开地址模态框
        async function openAddressModal(address = null) {
            currentAddressData = address;

            // 加载地址数据
            await loadAddressData();

            // 初始化地址选择器
            initAddressSelectors();

            if (address) {
                // 编辑模式
                document.getElementById('addressModalTitle').textContent = '编辑邮寄地址';
                document.getElementById('address_id').value = address.address_id;
                document.getElementById('recipient_name').value = address.name;
                document.getElementById('recipient_phone').value = address.phone_number;
                document.getElementById('detailed_address').value = address.detailed_address;

                // 设置地址选择器的值
                setTimeout(() => {
                    if (address.province) {
                        provinceSelect.setValue(address.province);
                        loadCitiesForSelect(address.province).then(() => {
                            if (address.city) {
                                citySelect.setValue(address.city);
                                loadDistrictsForSelect(address.city).then(() => {
                                    if (address.district) {
                                        districtSelect.setValue(address.district);
                                    }
                                });
                            }
                        });
                    }
                }, 100);
            } else {
                // 新增模式
                document.getElementById('addressModalTitle').textContent = '添加邮寄地址';
                document.getElementById('addressForm').reset();
                document.getElementById('address_id').value = '';
            }

            document.getElementById('addressModal').classList.remove('hidden');
        }

        // 关闭地址模态框
        function closeAddressModal() {
            document.getElementById('addressModal').classList.add('hidden');
            document.getElementById('addressForm').reset();
            currentAddressData = null;

            // 销毁地址选择器
            if (provinceSelect) {
                provinceSelect.destroy();
                provinceSelect = null;
            }
            if (citySelect) {
                citySelect.destroy();
                citySelect = null;
            }
            if (districtSelect) {
                districtSelect.destroy();
                districtSelect = null;
            }
        }

        // 加载地址数据
        async function loadAddressData() {
            try {
                const response = await fetch('/api/teacher/get_address_data');
                const data = await response.json();

                if (data.code === 0) {
                    // 将对象转换为数组格式，并转换字段名
                    addressFormData = Object.values(data.data).map(province => ({
                        name: province.n,
                        children: province.c ? Object.values(province.c).map(city => ({
                            name: city.n,
                            children: city.c ? Object.values(city.c).map(district => ({
                                name: district.n
                            })) : []
                        })) : []
                    }));
                } else {
                    showMessage('加载地址数据失败', 'error');
                }
            } catch (error) {
                showMessage('加载地址数据失败', 'error');
            }
        }

        // 初始化地址选择器
        function initAddressSelectors() {
            // 销毁之前的实例
            if (provinceSelect) provinceSelect.destroy();
            if (citySelect) citySelect.destroy();
            if (districtSelect) districtSelect.destroy();

            // 创建省份选择器
            provinceSelect = new CustomSelect('provinceSelectContainer', {
                placeholder: '请选择省份',
                searchPlaceholder: '搜索省份...',
                onSelect: function(value, text) {
                    loadCitiesForSelect(value);
                }
            });

            // 创建城市选择器
            citySelect = new CustomSelect('citySelectContainer', {
                placeholder: '请选择城市',
                searchPlaceholder: '搜索城市...',
                onSelect: function(value, text) {
                    loadDistrictsForSelect(value);
                }
            });

            // 创建区县选择器
            districtSelect = new CustomSelect('districtSelectContainer', {
                placeholder: '请选择区县',
                searchPlaceholder: '搜索区县...'
            });

            // 加载省份数据
            loadProvincesForSelect();
        }

        // 加载省份数据
        function loadProvincesForSelect() {
            if (addressFormData && provinceSelect) {
                const provinceOptions = addressFormData.map(province => ({
                    value: province.name,
                    text: province.name
                }));
                provinceSelect.setOptions(provinceOptions);
            }
        }

        // 加载城市数据
        async function loadCitiesForSelect(provinceName) {
            // 重置下级选择器
            citySelect.setOptions([]);
            citySelect.selectedValue = '';
            citySelect.selectedText = '';
            if (citySelect.textSpan) citySelect.textSpan.textContent = '请选择城市';

            districtSelect.setOptions([]);
            districtSelect.selectedValue = '';
            districtSelect.selectedText = '';
            if (districtSelect.textSpan) districtSelect.textSpan.textContent = '请选择区县';

            if (provinceName && addressFormData) {
                const province = addressFormData.find(p => p.name === provinceName);
                if (province && province.children) {
                    const cityOptions = province.children.map(city => ({
                        value: city.name,
                        text: city.name
                    }));
                    citySelect.setOptions(cityOptions);
                }
            }
        }

        // 加载区县数据
        async function loadDistrictsForSelect(cityName) {
            districtSelect.setOptions([]);
            districtSelect.selectedValue = '';
            districtSelect.selectedText = '';
            if (districtSelect.textSpan) districtSelect.textSpan.textContent = '请选择区县';

            const provinceName = provinceSelect.getValue();

            if (provinceName && cityName && addressFormData) {
                const province = addressFormData.find(p => p.name === provinceName);
                if (province && province.children) {
                    const city = province.children.find(c => c.name === cityName);
                    if (city && city.children) {
                        const districtOptions = city.children.map(district => ({
                            value: district.name,
                            text: district.name
                        }));
                        districtSelect.setOptions(districtOptions);
                    }
                }
            }
        }

        // 保存地址更改
        async function saveAddressChanges() {
            const formData = new FormData();
            const addressId = document.getElementById('address_id').value;
            const name = document.getElementById('recipient_name').value.trim();
            const phone = document.getElementById('recipient_phone').value.trim();
            const province = provinceSelect ? provinceSelect.getValue() : '';
            const city = citySelect ? citySelect.getValue() : '';
            const district = districtSelect ? districtSelect.getValue() : '';
            const detailedAddress = document.getElementById('detailed_address').value.trim();

            // 表单验证
            if (!name) {
                showMessage('请输入收件人姓名', 'error');
                return;
            }

            if (!phone) {
                showMessage('请输入联系电话', 'error');
                return;
            }

            if (!province || !city || !district) {
                showMessage('请选择完整的地址信息', 'error');
                return;
            }

            if (!detailedAddress) {
                showMessage('请输入详细地址', 'error');
                return;
            }

            // 构建表单数据
            if (addressId) {
                formData.append('address_id', addressId);
            }
            formData.append('name', name);
            formData.append('phone_number', phone);
            formData.append('province', province);
            formData.append('city', city);
            formData.append('district', district);
            formData.append('detailed_address', detailedAddress);

            try {
                const url = addressId ? '/api/teacher/edit_address' : '/api/teacher/add_address';
                const response = await fetch(url, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.code === 0) {
                    showMessage(addressId ? '地址修改成功' : '地址添加成功', 'success');
                    closeAddressModal();

                    // 延迟1.5秒后刷新页面，让用户看到成功提示
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showMessage(data.message || '保存失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误，请稍后重试', 'error');
            }
        }

    </script>

    <!-- Alpine.js 必须在函数定义之后加载 -->
    <script src="/static/js/alpine.min.js"></script>
</body>
</html>
